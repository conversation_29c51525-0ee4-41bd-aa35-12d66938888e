import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { ConfigModule } from '@nestjs/config';
import { CacheModule } from '@nestjs/cache-manager';
import apiConfig from './configs/apiConfig';
import mainConfig from './configs/mainConfig';
import { LoggingInterceptor } from './logging.interceptor';
import { UtilsModule } from './utils/utils.module';
import { UtilsService } from './utils/utils.service';
import { ApiModule } from './api/api.module';
import { GraylogModule } from './graylog/graylog.module';
import { UmsPermissionModule } from './resources/ums/ums_permission/ums_permission.module';
// import { AuthModule } from './auth/auth.module';
import { AuthModule } from './auth/auth.module';
import utilsConfig from './configs/utilsConfig';
import { ProgramsModule } from './resources/programs/programs.module';
import { QItemBlocksModule } from './resources/assessment-forms/quiz/item-blocks/item-blocks.module';
import { QHeaderBodiesModule } from './resources/assessment-forms/quiz/header-bodies/header-bodies.module';
import { QImageBodiesModule } from './resources/assessment-forms/quiz/image-bodies/image-bodies.module';
import { QSubmissionsModule } from './resources/assessment-forms/quiz/submissions/submissions.module';
import { QResponsesModule } from './resources/assessment-forms/quiz/responses/responses.module';
import { QOptionsModule } from './resources/assessment-forms/quiz/options/options.module';
import { QQuestionsModule } from './resources/assessment-forms/quiz/questions/questions.module';
import { PermissionsModule } from './resources/permissions/permissions.module';
import { EItemBlocksModule } from './resources/assessment-forms/evaluate/item-blocks/item-blocks.module';
import { EHeaderBodiesModule } from './resources/assessment-forms/evaluate/header-bodies/header-bodies.module';
import { EImageBodiesModule } from './resources/assessment-forms/evaluate/image-bodies/image-bodies.module';
import { EResponsesModule } from './resources/assessment-forms/evaluate/responses/responses.module';
import { EOptionsModule } from './resources/assessment-forms/evaluate/options/options.module';
import { EQuestionsModule } from './resources/assessment-forms/evaluate/questions/questions.module';
import { ESubmissionsModule } from './resources/assessment-forms/evaluate/submissions/submissions.module';
import { QuizAssessmentsModule } from './resources/assessment-forms/quiz/assessments/assessments.module';
import { EAssessmentsModule } from './resources/assessment-forms/evaluate/assessments/assessments.module';
import { RolesModule } from './resources/roles/roles.module';
import { UsersModule } from './resources/users/users.module';

// AUTOMATE_TEST_MODE = true -> เปิดการเชื่อม db เป็น localhost

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [apiConfig, mainConfig, utilsConfig],
      envFilePath: '.development.env', //can also specify multiple paths for .env files ex. ['.env.development.local', '.env.development'],
      isGlobal: true,
    }),
    CacheModule.register({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      // name: 'eOfficeDev',
      type: 'mysql',
      host:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? process.env.DB_TEST_HOST
          : process.env.DB_HOST,
      port:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? +process.env.DB_TEST_PORT
          : +process.env.DB_PORT,
      username:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? process.env.DB_TEST_USER
          : process.env.DB_USER,
      password:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? process.env.DB_TEST_PASSWORD
          : process.env.DB_PASSWORD,
      database:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? process.env.DB_TEST_NAME
          : process.env.DB_NAME,
      synchronize: false,
      migrationsRun: true,
      autoLoadEntities: true,
    }),
    UtilsModule,
    ApiModule,
    GraylogModule,
    // AuthModule,
    // EAssessmentsModule,
    UmsPermissionModule,
    AuthModule,
    ProgramsModule,
    PermissionsModule,
    RolesModule,
    UsersModule,
    QItemBlocksModule,
    QHeaderBodiesModule,
    QImageBodiesModule,
    QSubmissionsModule,
    QResponsesModule,
    QOptionsModule,
    QQuestionsModule,
    EItemBlocksModule,
    EAssessmentsModule,
    EHeaderBodiesModule,
    EImageBodiesModule,
    ESubmissionsModule,
    EResponsesModule,
    EOptionsModule,
    EQuestionsModule,
    QuizAssessmentsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    UtilsService,
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
  ],
})
export class AppModule {
  constructor(private dataSource: DataSource) {}
}
