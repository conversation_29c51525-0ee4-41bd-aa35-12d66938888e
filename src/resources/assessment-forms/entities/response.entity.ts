import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyTo<PERSON>ne,
  Join<PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { Submission } from './submission.entity';
import { Option } from './option.entity';
import { Question } from './question.entity';

@Entity('responses')
export class Response {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true, type: 'text' })
  answerText: string | null;

  @Column()
  submissionId: number;

  @Column({ nullable: true })
  selectedOptionId: number | null;

  @Column()
  questionId: number;

  @ManyToOne(() => Submission, (submission) => submission.responses)
  @JoinColumn({ name: 'submissionId' })
  submission: Submission;

  @ManyToOne(() => Option, (option) => option.responses, { nullable: true })
  @JoinColumn({ name: 'selectedOptionId' })
  selectedOption: Option;

  @ManyToOne(() => Question, (question) => question.responses)
  @JoinColumn({ name: 'questionId' })
  question: Question;
}
