import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  Query,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags, ApiConsumes } from '@nestjs/swagger';
import { EAssessmentsService } from './assessments.service';
import { CreateAssessmentDto } from '../../dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from '../../dto/updates/update-assessment.dto';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { FindAssessmentDto } from '../../dto/find-assessment.dto';
import { AssessmentType } from '../../enums/assessment-type.enum';
import type { DataParams } from 'src/types/params';

@ApiTags('Evaluate-Assessments')
@Controller('evaluate/assessments')
export class EAssessmentsController {
  constructor(private readonly assessmentsService: EAssessmentsService) {}

  @Post()
  @ApiOperation({
    summary: 'สร้างแบบสอบถามใหม่',
    description: 'สร้างแบบสอบถาม (Evaluate) ใหม่ตาม template ที่กำหนด',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับสร้างแบบประเมิน (แบบฟอร์ม)',
    schema: {
      type: 'object',
      properties: {
        creatorUserId: { type: 'integer', example: 1 },
        programId: { type: 'integer', example: 1 },
        type: {
          type: 'string',
          enum: Object.values(AssessmentType),
          example: AssessmentType.FORM,
        },
      },
      required: ['creatorUserId', 'programId', 'type'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  async create(@Body() body: CreateAssessmentDto) {
    console.log(body);
    return this.assessmentsService.createOne(body);
  }

  @Post('list')
  async getAll(@Body() body: FindAssessmentDto) {
    const pagination: DataParams = {
      sortBy: body.sortBy ?? null,
      order: body.order ?? 'ASC',
      limit: body.limit ?? 10,
      page: body.page ?? 1,
      search: body.search ?? '',
    };
    return this.assessmentsService.getAll(body.programId, pagination);
  }

  @Get('name')
  async getAllName() {
    return this.assessmentsService.getAllName();
  }

  @Get(':id')
  getOne(@Param('id') id: number, @Query('section') section: number) {
    return this.assessmentsService.findOneBySection(+id, +section);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateAssessmentDto: UpdateAssessmentDto,
  ) {
    return this.assessmentsService.update(+id, updateAssessmentDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.assessmentsService.remove(+id);
  }
}
