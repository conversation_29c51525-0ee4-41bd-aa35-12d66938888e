import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EAssessmentsService } from './assessments.service';
import { EAssessmentsController } from './assessments.controller';
import { Assessment } from '../../entities/assessment.entity';
import { EItemBlocksModule } from '../item-blocks/item-blocks.module';

@Module({
  imports: [TypeOrmModule.forFeature([Assessment]),EItemBlocksModule],
  controllers: [EAssessmentsController],
  providers: [EAssessmentsService],
  exports: [EAssessmentsService],
})
export class EAssessmentsModule {}
