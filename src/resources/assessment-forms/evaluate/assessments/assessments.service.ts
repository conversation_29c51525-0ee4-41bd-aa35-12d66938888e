import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Assessment } from '../../entities/assessment.entity';
import { Repository } from 'typeorm';
import { AssessmentType } from '../../enums/assessment-type.enum';
import { v4 as uuidv4 } from 'uuid';
import { User } from 'src/resources/users/entities/user.entity';
import { Program } from 'src/resources/programs/entities/program.entity';
import { CreateAssessmentDto } from '../../dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from '../../dto/updates/update-assessment.dto';
import type { DataParams } from 'src/types/params';
import { EItemBlocksService } from '../item-blocks/item-blocks.service';
import { ItemBlockType } from '../../enums/item-block-type.enum';

@Injectable()
export class EAssessmentsService {
  constructor(
    @InjectRepository(Assessment)
    private readonly assessmentRepo: Repository<Assessment>,
    private readonly itemBlocksService: EItemBlocksService,
  ) {}
  async createOne(dto: CreateAssessmentDto) {
    const { creatorUserId, programId, type } = dto;

    const assessment = this.assessmentRepo.create({
      creator: { id: creatorUserId } as User,
      program: { id: programId } as Program,
      name: 'แบบฟอร์มไม่มีชื่อ',
      type,
      linkURL: `https://example.com/evaluate/${uuidv4()}`,
      responseEdit: false,
      status: false,
      totalScore: 1,
      timeout: -1,
      passRatio: 1,
      submitLimit: 1,
      isPrototype: false,
      startAt: null,
      endAt: null,
    });
    const savedAssessment = await this.assessmentRepo.save(assessment);
    const headerBlock = await this.itemBlocksService.createBlockHeader({
      assessmentId: savedAssessment.id,
      sequence: 1,
      section: 1,
      type: ItemBlockType.HEADER,
      isRequired: false,
    });
    const blockRadio = await this.itemBlocksService.createBlock({
      assessmentId: savedAssessment.id,
      sequence: 2,
      section: 1,
      type: ItemBlockType.RADIO,
      isRequired: false,
    });
    const result = await this.assessmentRepo.findOne({
      where: { id: savedAssessment.id },
      relations: ['creator', 'program'],
    });
    if (!result) {
      throw new NotFoundException('Assessment not found');
    }

    return {
      id: result.id,
      creatorUserId: result.creator?.id,
      programId: result.program?.id,
      name: result.name,
      type: result.type,
      createdAt: result.createdAt?.toISOString(),
      startAt: result.startAt ? result.startAt.toISOString() : null,
      endAt: result.endAt ? result.endAt.toISOString() : null,
      submitLimit: result.submitLimit,
      linkURL: result.linkURL,
      responseEdit: result.responseEdit,
      status: result.status,
      totalScore: result.totalScore,
      timeout: result.timeout,
      passRatio: result.passRatio,
      isPrototype: result.isPrototype,
      itemBlocks: [headerBlock, blockRadio],
    };
  }

  getAll(programId: number, pagination: DataParams) {
    const { sortBy, order, limit, page, search } = pagination;

    const query = this.assessmentRepo
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.creator', 'creator')
      .where('assessment.type = :type', { type: AssessmentType.FORM })
      .andWhere('assessment.programId = :programId', { programId });
    if (search) {
      query.andWhere('assessment.name LIKE :search', { search: `%${search}%` });
    }

    if (sortBy) {
      query.orderBy(`assessment.${sortBy}`, order);
    }

    query.skip((page - 1) * limit).take(limit);

    return query.getMany();
  }

  getAllName() {
    return this.assessmentRepo.find({
      select: ['id', 'name'],
      order: { id: 'ASC' },
    });
  }

  async findOneBySection(id: number, section: number) {
    if (!id || isNaN(id)) {
      throw new BadRequestException('Invalid assessment ID');
    }

    return this.assessmentRepo
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.itemBlocks', 'itemBlocks')
      .leftJoinAndSelect('itemBlocks.questions', 'questions')
      .leftJoinAndSelect('itemBlocks.options', 'options')
      .leftJoinAndSelect('itemBlocks.headerBody', 'headerBody')
      .where('assessment.id = :id', { id })
      .andWhere('assessment.type = :type', { type: AssessmentType.FORM })
      .andWhere('itemBlocks.section = :section', { section })
      .getOne();
  }

  async update(id: number, updateAssessmentDto: UpdateAssessmentDto) {
    const assessment = await this.assessmentRepo.findOne({
      where: { id },
      relations: ['program', 'creator'],
    });

    if (!assessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    // Merge the existing assessment with the incoming DTO
    const updated = this.assessmentRepo.merge(assessment, updateAssessmentDto);

    // Save the merged entity
    await this.assessmentRepo.save(updated);

    return updated;
  }

  async remove(id: number) {
    const assessment = await this.assessmentRepo.findOne({
      where: { id },
      relations: [
        'itemBlocks',
        'itemBlocks.questions',
        'itemBlocks.options',
        'itemBlocks.headerBody',
        'itemBlocks.imageBody',
        'submissions',
        'submissions.responses',
      ],
    });

    if (!assessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    // ลบ responses
    for (const submission of assessment.submissions) {
      if (submission.responses.length) {
        await this.assessmentRepo.manager.remove(submission.responses);
      }
    }

    // ลบ submissions
    if (assessment.submissions.length) {
      await this.assessmentRepo.manager.remove(assessment.submissions);
    }

    // ลบ questions
    for (const block of assessment.itemBlocks) {
      if (block.questions.length) {
        await this.assessmentRepo.manager.remove(block.questions);
      }
    }

    // ลบ options
    for (const block of assessment.itemBlocks) {
      if (block.options.length) {
        await this.assessmentRepo.manager.remove(block.options);
      }
    }

    // ลบ headerBody & imageBody (OneToOne)
    for (const block of assessment.itemBlocks) {
      if (block.headerBody) {
        await this.assessmentRepo.manager.remove(block.headerBody);
      }
      if (block.imageBody) {
        await this.assessmentRepo.manager.remove(block.imageBody);
      }
    }

    // ลบ itemBlocks
    if (assessment.itemBlocks.length) {
      await this.assessmentRepo.manager.remove(assessment.itemBlocks);
    }

    // ลบ assessment ตัวเอง
    await this.assessmentRepo.remove(assessment);

    return {
      success: true,
      message: `Assessment #${id} and related data deleted.`,
    };
  }
}
