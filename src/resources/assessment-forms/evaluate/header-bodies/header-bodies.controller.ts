import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
} from '@nestjs/common';
import { EHeaderBodiesService } from './header-bodies.service';
import { ApiTags, ApiOperation, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { CreateHeaderBodyDto } from '../../dto/creates/create-header-body.dto';
import { UpdateHeaderBodyDto } from '../../dto/updates/update-header-body.dto';
import { AnyFilesInterceptor } from '@nestjs/platform-express';

@ApiTags('Evaluate-Header-Bodies')
@Controller('evaluate/header-bodies')
export class EHeaderBodiesController {
  constructor(private readonly headerBodiesService: EHeaderBodiesService) {}

  @Post()
   @ApiOperation({
      summary: 'สร้าง Header Bodyใหม่',
      description: 'สร้าง Header Body ใหม่สำหรับ (Evaluate)',
    })
    @ApiConsumes('multipart/form-data')
    @ApiBody({
      description: 'ข้อมูลสำหรับสร้าง Header Body ใหม่',
      schema: {
        type: 'object',
        properties: {
          itemBlockId: { type: 'integer', example: 1 },
          title: { type: 'string', example: 'หัวข้อที่ 1' },
          description: { type: 'string', example: 'คำอธิบายหัวข้อที่ 1' },
        },
        required: ['itemBlockId'],
      },
    })
    @UseInterceptors(AnyFilesInterceptor())
  create(@Body() createHeaderBodyDto: CreateHeaderBodyDto) {
    return this.headerBodiesService.createHeader(createHeaderBodyDto);
  }

  @Get()
  findAll() {
    return this.headerBodiesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.headerBodiesService.findOne(+id);
  }

  @Patch(':id')
   @ApiOperation({
      summary: 'อัพเดท Header Bodyใหม่',
      description: 'อัพเดท Header Body ใหม่สำหรับ (Evaluate)',
    })
    @ApiConsumes('multipart/form-data')
    @ApiBody({
      description: 'ข้อมูลสำหรับอัพเดท Header Body ',
      schema: {
        type: 'object',
        properties: {
          itemBlockId: { type: 'integer', example: 1 },
          title: { type: 'string', example: 'หัวข้อที่ 1' },
          description: { type: 'string', example: 'คำอธิบายหัวข้อที่ 1' },
        },
        required: ['itemBlockId'],
      },
    })
    @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param('id') id: string,
    @Body() updateHeaderBodyDto: UpdateHeaderBodyDto,
  ) {
    return this.headerBodiesService.update(+id, updateHeaderBodyDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.headerBodiesService.remove(+id);
  }
}
