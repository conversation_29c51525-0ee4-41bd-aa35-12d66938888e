import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EHeaderBodiesService } from './header-bodies.service';
import { EHeaderBodiesController } from './header-bodies.controller';
import { HeaderBody } from '../../entities/header-body.entity';
import { ItemBlock } from '../../entities/item-block.entity';

@Module({
  imports: [TypeOrmModule.forFeature([HeaderBody,ItemBlock])],
  controllers: [EHeaderBodiesController],
  providers: [EHeaderBodiesService],
  exports: [EHeaderBodiesService],
})
export class EHeaderBodiesModule {}
