import { Injectable } from '@nestjs/common';
import { CreateHeaderBodyDto } from '../../dto/creates/create-header-body.dto';
import { UpdateHeaderBodyDto } from '../../dto/updates/update-header-body.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { HeaderBody } from '../../entities/header-body.entity';
import { Repository } from 'typeorm';
import { ItemBlock } from '../../entities/item-block.entity';

@Injectable()
export class EHeaderBodiesService {
  constructor(
    @InjectRepository(HeaderBody)
    private readonly headerBodyRepository: Repository<HeaderBody>,
    @InjectRepository(ItemBlock)
    private readonly itemBlockRepository: Repository<ItemBlock>,
  ) {}
  async createHeader(createHeaderBodyDto: CreateHeaderBodyDto) {
    const { itemBlockId, title, description, ...rest } = createHeaderBodyDto;
    const itemBlock = await this.itemBlockRepository.findOne({
      where: { id: itemBlockId },
    });

    if (!itemBlock) {
      throw new Error(`ItemBlock with ID ${itemBlockId} not found`);
    }

    const headerBody = this.headerBodyRepository.create({
      ...rest,
      title: title?.trim() || 'แบบฟอร์มไม่มีชื่อ',
      description: description?.trim() || null,
      itemBlock, 
    });

    const saved = await this.headerBodyRepository.save(headerBody);

    return {
      id: saved.id,
      title: saved.title,
      description: saved.description,
      itemBlockId: saved.itemBlock.id,
      ...rest, 
    };
  }

  findAll() {
    return `This action returns all header bodies`;
  }

  findOne(id: number) {
    return `This action returns a #${id} header body`;
  }

  async update(id: number, updateHeaderBodyDto: UpdateHeaderBodyDto) {
    const { itemBlockId, title, description, ...rest } = updateHeaderBodyDto;
    const headerBody = await this.headerBodyRepository.findOne({
      where: { id },
      relations: ['itemBlock'], 
    });

    if (!headerBody) {
      throw new Error(`HeaderBody with ID ${id} not found`);
    }
    if (itemBlockId) {
      const itemBlock = await this.itemBlockRepository.findOne({
        where: { id: itemBlockId },
      });

      if (!itemBlock) {
        throw new Error(`ItemBlock with ID ${itemBlockId} not found`);
      }

      headerBody.itemBlock = itemBlock; 
    }
    headerBody.title = title ?? headerBody.title;
    headerBody.description =
      description === '' ? null : (description ?? headerBody.description);

    Object.assign(headerBody, rest);
    const updated = await this.headerBodyRepository.save(headerBody);

    return {
      id: updated.id,
      title: updated.title,
      description: updated.description,
      itemBlockId: updated.itemBlock.id,
      ...rest, 
    };
  }

  remove(id: number) {
    return `This action removes a #${id} header body`;
  }
}
