import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EImageBodiesService } from './image-bodies.service';
import { EImageBodiesController } from './image-bodies.controller';
import { ImageBody } from '../../entities/image-body.entity';
import { ItemBlock } from '../../entities/item-block.entity';
import { FileUploadModule } from '../utils/file-upload.module';

@Module({
  imports: [TypeOrmModule.forFeature([ImageBody,ItemBlock]),
  FileUploadModule],
  controllers: [EImageBodiesController],
  providers: [EImageBodiesService],
  exports: [EImageBodiesService],
})
export class EImageBodiesModule {}
