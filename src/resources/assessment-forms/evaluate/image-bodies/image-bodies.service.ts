import { Injectable } from '@nestjs/common';
import { CreateImageBodyDto } from '../../dto/creates/create-image-body.dto';
import { UpdateImageBodyDto } from '../../dto/updates/update-image-body.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { ImageBody } from '../../entities/image-body.entity';
import { Repository } from 'typeorm';
import { ItemBlock } from '../../entities/item-block.entity';
import { FileUploadService } from '../utils/file-upload.service';

@Injectable()
export class EImageBodiesService {
  constructor(
    @InjectRepository(ImageBody)
    private readonly imageBodyRepository: Repository<ImageBody>,
    @InjectRepository(ItemBlock)
    private readonly itemBlockRepository: Repository<ItemBlock>,
    private readonly fileUploadService: FileUploadService,
  ) {}

  async create(
    createImageBodyDto: CreateImageBodyDto,
    file?: Express.Multer.File, 
  ) {
    const { itemBlockId, imagePath, imageHeight, imageWidth, ...rest } = createImageBodyDto;

    const itemBlock = await this.itemBlockRepository.findOne({
      where: { id: itemBlockId },
    });

    if (!itemBlock) {
      throw new Error('ItemBlock with the given ID is not found');
    }
    let finalImagePath = imagePath?.trim() || null;
    if (file) {
      finalImagePath = await this.fileUploadService.uploadAndGetImagePath(file);
    }
    const imageBody = this.imageBodyRepository.create({
      ...rest,
      imagePath: finalImagePath,
      imageText: rest.imageText?.trim() || null,
      imageHeight: Number(imageHeight) || 0,
      imageWidth: Number(imageWidth) || 0,
      itemBlock,
    });

    const saved = await this.imageBodyRepository.save(imageBody);

    return {
      id: saved.id,
      itemBlockId: saved.itemBlock.id,
      imagePath: saved.imagePath,
      imageHeight: saved.imageHeight,
      imageWidth: saved.imageWidth,
      ...rest,
    };
  }

  findAll() {
    return `This action returns all image bodies`;
  }

  findOne(id: number) {
    return `This action returns a #${id} image body`;
  }

  async update(
    id: number,
    updateImageBodyDto: UpdateImageBodyDto,
    file?: Express.Multer.File,
  ) {
    const { itemBlockId, imagePath, imageHeight, imageWidth, imageText,...rest } = updateImageBodyDto;
    const imageBody = await this.imageBodyRepository.findOne({
      where: { id },
      relations: ['itemBlock'],
    });

    if (!imageBody) {
      throw new Error(`ImageBody with ID ${id} not found`);
    }
    if (itemBlockId) {
      const itemBlock = await this.itemBlockRepository.findOne({
        where: { id: itemBlockId },
      });
      imageBody.itemBlock = itemBlock;
    }

    let newImagePath = imagePath?.trim() || null;
    if (file) {
      newImagePath = await this.fileUploadService.uploadAndGetImagePath(file);
      if (imageBody.imagePath) {
        await this.fileUploadService.deleteFileByUrl(imageBody.imagePath);
      }
    }
    else if (newImagePath === null && imageBody.imagePath) {
      await this.fileUploadService.deleteFileByUrl(imageBody.imagePath);
    }

    imageBody.imagePath = newImagePath;
    imageBody.imageHeight = Number(imageHeight) || 0;
    imageBody.imageWidth = Number(imageWidth) || 0;
    imageBody.imageText = imageText?.trim() || null;

    Object.assign(imageBody, rest);

    const updated = await this.imageBodyRepository.save(imageBody);

    return {
      id: updated.id,
      itemBlockId: updated.itemBlock.id,
      imagePath: updated.imagePath,
      imageHeight: updated.imageHeight,
      imageWidth: updated.imageWidth,
      imageText: updated.imageText,
      ...rest,
    };
  }


  remove(id: number) {
    return `This action removes a #${id} image body`;
  }
}
