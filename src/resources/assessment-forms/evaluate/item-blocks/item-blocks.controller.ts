import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { EItemBlocksService } from './item-blocks.service';
import { CreateItemBlockDto } from '../../dto/creates/create-item-block.dto';
import { UpdateItemBlockDto } from '../../dto/updates/update-item-block.dto';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { ItemBlockType } from '../../enums/item-block-type.enum';

@ApiTags('Evaluate-ItemBlocks')
@Controller('evaluate/item-blocks')
export class EItemBlocksController {
  constructor(private readonly itemBlocksService: EItemBlocksService) {}

  @Post('block')
  @ApiOperation({
    summary: 'สร้าง Item Block ใหม่',
    description: 'สร้าง Item Block ใหม่สำหรับ (Evaluate)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับสร้าง Item Block ใหม่',
    schema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: Object.values(ItemBlockType),
          example: ItemBlockType.RADIO,
        },
        sequence: {
          type: 'integer',
          example: 1,
          default: 1,
        },
        assessmentId: { type: 'integer', example: 1 },
      },
      required: ['assessmentId'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() body: CreateItemBlockDto) {
    console.log(body);
    return this.itemBlocksService.createBlock(body);
  }

  async findAll(
    @Param('assessmentId', ParseIntPipe) assessmentId: number,
    @Query('page', ParseIntPipe) page: number,
  ) {
    return this.itemBlocksService.findAll(assessmentId, page);
  }

  @Get(':assessmentId/block')
  async findOne(@Param('assessmentId', ParseIntPipe) assessmentId: number) {
    return this.itemBlocksService.findOne(assessmentId);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'อัพเดท Item Block ใหม่',
    description: 'อัพเดท Item Block ใหม่สำหรับ (Evaluate)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับอัพเดท Item Block ใหม่',
    schema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: [
            ItemBlockType.CHECKBOX,
            ItemBlockType.GRID,
            ItemBlockType.RADIO,
            ItemBlockType.TEXTFIELD,
          ],
          example: ItemBlockType.RADIO,
        },
        sequence: {
          type: 'integer',
          example: 1,
          default: 1,
        },
      },
      required: ['type'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param('id') id: string,
    @Body() updateItemBlockDto: UpdateItemBlockDto,
  ) {
    return this.itemBlocksService.update(+id, updateItemBlockDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.itemBlocksService.remove(id);
  }
}
