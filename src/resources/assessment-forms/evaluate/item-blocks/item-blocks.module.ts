import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EItemBlocksService } from './item-blocks.service';
import { EItemBlocksController } from './item-blocks.controller';
import { ItemBlock } from '../../entities/item-block.entity';
import { Assessment } from '../../entities/assessment.entity';
import { Question } from '../../entities/question.entity';
import { Option } from '../../entities/option.entity';
import { HeaderBody } from '../../entities/header-body.entity';
import { ImageBody } from '../../entities/image-body.entity';
import { EQuestionsModule } from '../questions/questions.module';
import { EHeaderBodiesModule } from '../header-bodies/header-bodies.module';
import { EOptionsModule } from '../options/options.module';

@Module({
  imports: [TypeOrmModule.forFeature([ItemBlock, Assessment, Question, Option, HeaderBody, ImageBody]),EQuestionsModule,EHeaderBodiesModule,EOptionsModule],
  controllers: [EItemBlocksController],
  providers: [EItemBlocksService],
  exports: [EItemBlocksService],
})
export class EItemBlocksModule {}
