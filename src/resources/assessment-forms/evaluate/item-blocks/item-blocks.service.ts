import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateItemBlockDto } from '../../dto/creates/create-item-block.dto';
import { UpdateItemBlockDto } from '../../dto/updates/update-item-block.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { ItemBlock } from '../../entities/item-block.entity';
import { In, Repository } from 'typeorm';
import { Assessment } from '../../entities/assessment.entity';
import { Question } from '../../entities/question.entity';
import { Option } from '../../entities/option.entity';
import { HeaderBody } from '../../entities/header-body.entity';
import { ImageBody } from '../../entities/image-body.entity';
import { EQuestionsService } from '../questions/questions.service';
import { ItemBlockType } from '../../enums/item-block-type.enum';
import { EHeaderBodiesService } from '../header-bodies/header-bodies.service';
import { EOptionsService } from '../options/options.service';

@Injectable()
export class EItemBlocksService {
  constructor(
    @InjectRepository(ItemBlock)
    private readonly itemBlockRepository: Repository<ItemBlock>,
    @InjectRepository(Assessment)
    private readonly assessmentRepo: Repository<Assessment>,
    @InjectRepository(HeaderBody)
    private readonly headerBodyRepository: Repository<HeaderBody>,
    @InjectRepository(ImageBody)
    private readonly imageBodyRepository: Repository<ImageBody>,
    private readonly questionService: EQuestionsService,
    private readonly headerBodiesService: EHeaderBodiesService,
    private readonly optionsService: EOptionsService,
  ) {}

  async getMaxSequence(assessmentId: number): Promise<number> {
    const lastItemBlock = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.sequence', 'DESC')
      .getOne();

    return lastItemBlock ? lastItemBlock.sequence + 1 : 1;
  }

  async getMaxSectionNumber(assessmentId: number): Promise<number> {
    const lastSection = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.section', 'DESC')
      .getOne();

    return lastSection ? lastSection.section + 1 : 1;
  }

  async handleType(type: string, savedItemBlock: ItemBlock) {
    let result = savedItemBlock;

    const createQuestionAndOption = async (
      itemBlockId: number,
      includeHeader: boolean = true,
      withOption: boolean = true,
    ) => {
      const questions = [];

      if (includeHeader) {
        const headerQuestion = await this.questionService.create({
          itemBlockId,
          isHeader: false,
        });
        const fullHeaderQuestion = await this.questionService.findOne(
          headerQuestion.id,
        );
        questions.push(fullHeaderQuestion);
      }

      const question = await this.questionService.create({
        itemBlockId,
        isHeader: true,
      });
      const fullQuestion = await this.questionService.findOne(question.id);
      questions.push(fullQuestion);

      result.questions = questions;

      if (withOption) {
        const option = await this.optionsService.create({
          itemBlockId,
          value: 1,
          sequence: 1,
          nextSection: 1,
        });
        const fullOption = await this.optionsService.findOne(option.id);
        result.options = [fullOption];
      }
    };

    // Type not to create Question and Option
    const shouldCreateQuestionAndOption = ![
      ItemBlockType.HEADER,
      ItemBlockType.IMAGE,
    ].includes(type as ItemBlockType);

    if (shouldCreateQuestionAndOption) {
      // if type = Grid set includeHeader = false
      const includeHeader = type === ItemBlockType.GRID;
      // want to create Option
      const withOption = [
        ItemBlockType.RADIO,
        ItemBlockType.CHECKBOX,
        ItemBlockType.GRID,
      ].includes(type as ItemBlockType);
      await createQuestionAndOption(
        savedItemBlock.id,
        includeHeader,
        withOption,
      );
    }

    // Apply base logic to all types

    // HEADER-specific logic
    if (type === ItemBlockType.HEADER) {
      const headerBodyCreated = await this.headerBodiesService.createHeader({
        itemBlockId: savedItemBlock.id,
      });
      // Fetch the full HeaderBody entity including the required 'itemBlock' relation
      const headerBody = await this.headerBodyRepository.findOne({
        where: { id: headerBodyCreated.id },
        relations: ['itemBlock'],
      });
      result.headerBody = headerBody;
    }

    // IMAGE-specific logic
    if (type === ItemBlockType.IMAGE) {
      const imageBody = this.imageBodyRepository.create({
        itemBlockId: savedItemBlock.id,
        imageHeight: null,
        imageText: null,
        imagePath: null,
        imageWidth: null,
      });
      const savedImageBody = await this.imageBodyRepository.save(imageBody);
      result.imageBody = savedImageBody;
    }

    return result;
  }

  async createBlock(dto: CreateItemBlockDto) {
    const { assessmentId, type, sequence, ...rest } = dto;

    const assessment = await this.assessmentRepo.findOne({
      where: { id: assessmentId },
    });
    if (!assessment) {
      throw new NotFoundException(
        `Assessment with ID ${assessmentId} not found`,
      );
    }

    const itemBlock = this.itemBlockRepository.create({
      ...rest,
      isRequired: rest.isRequired ?? false,
      type,
      sequence : sequence ?? (await this.getMaxSequence(assessmentId)),
      assessment,
    });

    const saved = await this.itemBlockRepository.save(itemBlock);
    const resultType = await this.handleType(type, saved);

    const result = await this.itemBlockRepository.findOne({
      where: { id: saved.id },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });
    console.log('Result Question', result.questions);

    return {
      id: saved.id,
      sequence: saved.sequence,
      section: saved.section,
      type: saved.type,
      isRequired: saved.isRequired ?? false,
      assessmentId: saved.assessment.id,
      questions: resultType.questions ?? null,
      options: resultType.options ?? null,
      headerBody: resultType.headerBody ?? null,
      imageBody: resultType.imageBody ?? null,
    };
  }

  async createBlockHeader(dto: CreateItemBlockDto) {
    const assessment = await this.assessmentRepo.findOne({
      where: { id: dto.assessmentId },
    });
    const itemBlock = this.itemBlockRepository.create({
      type: ItemBlockType.HEADER,
      sequence: dto.sequence ?? 1,
      section: dto.section ?? 1,
      isRequired: false,
      assessment,
    });
    const savedBlock = await this.itemBlockRepository.save(itemBlock);
    const headerBody = await this.headerBodiesService.createHeader({
      itemBlockId: savedBlock.id,
    });

    return {
      id: savedBlock.id,
      type: savedBlock.type,
      sequence: savedBlock.sequence,
      section: savedBlock.section,
      isRequired: savedBlock.isRequired,
      assessmentId: savedBlock.assessment.id,
      headerBody,
    };
  }

  findAll(assessmentId: number, page: number) {
    return this.itemBlockRepository.find({
      where: {
        assessment: { id: assessmentId },
        section: page,
      },
      relations: ['questions', 'options'],
    });
  }

  findOne(assessmentId: number) {
    return this.itemBlockRepository.findOne({
      where: {
        assessment: { id: assessmentId },
      },
      relations: ['questions', 'options'],
    });
  }

  async update(
    id: number,
    updateItemBlockDto: UpdateItemBlockDto,
  ): Promise<ItemBlock> {
    const item = await this.itemBlockRepository.findOne({
      where: { id },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });

    if (!item) {
      throw new NotFoundException(`ItemBlock with ID ${id} not found`);
    }

    const oldType = item.type;
    const newType = updateItemBlockDto.type;

    const updatedItem = Object.assign(item, updateItemBlockDto);
    const savedItem = await this.itemBlockRepository.save(updatedItem);

    // === Type Check Helpers ===
    const typeHasOptions = (type: string) =>
      [
        ItemBlockType.RADIO,
        ItemBlockType.CHECKBOX,
        ItemBlockType.GRID,
      ].includes(type as ItemBlockType);

    const typeHasQuestion = (type: string) =>
      ![ItemBlockType.IMAGE, ItemBlockType.HEADER].includes(
        type as ItemBlockType,
      );

    const isGrid = (type: string) => type === ItemBlockType.GRID;
    const isTextField = (type: string) => type === ItemBlockType.TEXTFIELD;

    // === 1. GRID → อื่น: ลบ non-header questions ===
    if (isGrid(oldType) && !isGrid(newType)) {
      for (const q of item.questions) {
        if (!q.isHeader) {
          console.log('Grid Remove');
          await this.questionService.remove(q.id);
        } else {
          await this.questionService.update(q.id, q);
        }
      }
    }

    if (!isGrid(oldType) && isGrid(newType)) {
      const question = await this.questionService.create({
        itemBlockId: id,
        isHeader: false,
      });
      savedItem.questions = [
        ...(savedItem.questions || []),
        await this.questionService.findOne(question.id),
      ];
    }

    // === 2. TEXTFIELD → type ที่มี option: สร้าง option ใหม่ ===
    if (isTextField(oldType) && typeHasOptions(newType)) {
      const newOption = await this.optionsService.create({
        itemBlockId: id,
        value: 1,
        sequence: 1,
        nextSection: 1,
      });
      savedItem.options = [await this.optionsService.findOne(newOption.id)];
    }

    // === 3. มี options → มี options: อัปเดต options เดิม ===
    else if (typeHasOptions(oldType) && typeHasOptions(newType)) {
      for (const option of item.options) {
        await this.optionsService.update(option.id, option);
      }
    }

    // === 4. type ใหม่ไม่มี option → ลบ option ===
    if (!typeHasOptions(newType)) {
      await this.optionsService.removeByItemBlockId(id);
    }

    // === 5. type ใหม่ไม่มี question → ลบ question ===
    if (!typeHasQuestion(newType)) {
      await this.questionService.removeByItemBlockId(id);
    }

    return this.itemBlockRepository.findOne({
      where: { id },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });
  }

  async remove(id: number): Promise<void> {
    const item = await this.itemBlockRepository.findOne({
      where: { id },
      relations: ['questions', 'options', 'headerBody', 'imageBody'],
    });

    if (!item) {
      throw new NotFoundException(`ItemBlock with ID ${id} not found`);
    }

    // ลบข้อมูลสัมพันธ์
    if (item.questions?.length) {
      await this.questionService.removeByItemBlockId(id);
    }

    if (item.options?.length) {
      await this.optionsService.removeByItemBlockId(id);
    }

    if (item.headerBody) {
      await this.headerBodyRepository.delete({ itemBlockId: id });
    }

    if (item.imageBody) {
      await this.imageBodyRepository.delete({ itemBlockId: id });
    }

    // ลบ itemBlock หลัก
    await this.itemBlockRepository.delete(id);
  }
}
