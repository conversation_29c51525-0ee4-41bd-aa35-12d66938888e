import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { EOptionsService } from './options.service';

import { ApiTags, ApiOperation, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { CreateOptionDto } from '../../dto/creates/create-option.dto';
import { UpdateOptionDto } from '../../dto/updates/update-option.dto';
import { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { ApiService } from 'src/api/api.service';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';

@ApiTags('Evaluate-Options')
@Controller('evaluate/options')
export class EOptionsController {
  constructor(
    private readonly optionsService: EOptionsService, 
    private readonly apiService: ApiService
  ) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        optionText: { type: 'string', example: 'optionText' },
        value: { type: 'number', example: 1 },
        nextSection: { type: 'number' , example: 1 },
        itemBlockId: { type: 'number'   , example: 1 },
        sequence: { type: 'number', example: 1 },
        imagePath: {
          type: 'string',
          format: 'binary', 
        },
      }
    },
  })
  @UseInterceptors(
    FileInterceptor('imagePath', {
      storage: diskStorage({
        destination: './uploaded_file',
        filename: (req, file, cb) => {
          const ext = extname(file.originalname);
          const name = uuidv4();
          cb(null, name + ext); 
        },
      }),
    }),
  )
  async create(
    @Body() createOptionDto: CreateOptionDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.optionsService.create(createOptionDto, file);
  }
  
  @Get()
  findAll() {
    return this.optionsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.optionsService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'อัปเดตตัวเลือก',
    description: 'อัปเดตตัวเลือก (Question) ตาม template ที่กำหนด',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับอัปเดตตัวเลือก (Question)',
    schema: {
      type: 'object',
      properties: {
        optionText: { type: 'string', example: 'ExampleOption' },
        value: { type: 'integer', example: 1 },
        sequence: { type: 'integer', example: 1 },
        nextSection: { type: 'integer', example: 1 },
        itemBlockId: { type: 'integer', example: 1 },
        imagePath: {
          type: 'string',
          format: 'binary',
        },
      }
    },
  })
  @UseInterceptors(
    FileInterceptor('imagePath', {
      storage: diskStorage({
        destination: './uploaded_file',
        filename: (req, file, cb) => {
          const ext = extname(file.originalname);
          const name = uuidv4();
          cb(null, name + ext);
        },
      }),
    }),
  )
  async update(
    @Param('id') id: number,
    @Body() updateOptionDto: UpdateOptionDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.optionsService.update(id, updateOptionDto, file);
  }


  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.optionsService.remove(+id);
  }
}
