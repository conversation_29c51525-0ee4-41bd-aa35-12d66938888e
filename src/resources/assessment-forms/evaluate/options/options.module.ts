import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EOptionsService } from './options.service';
import { EOptionsController } from './options.controller';
import { Option } from '../../entities/option.entity';
import { Question } from '../../entities/question.entity';
import { ItemBlock } from '../../entities/item-block.entity';
import { FileUploadModule } from '../utils/file-upload.module';

@Module({
  imports: [TypeOrmModule.forFeature([Option, Question, ItemBlock])
  ,FileUploadModule],
  controllers: [EOptionsController],
  providers: [EOptionsService],
  exports: [EOptionsService],
})
export class EOptionsModule {}
