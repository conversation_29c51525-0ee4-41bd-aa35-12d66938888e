import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EQuestionsService } from './questions.service';
import { EQuestionsController } from './questions.controller';
import { Question } from '../../entities/question.entity';
import { ItemBlock } from '../../entities/item-block.entity';
import { FileUploadModule } from '../utils/file-upload.module';

@Module({
  imports: [TypeOrmModule.forFeature([Question, ItemBlock])
  ,FileUploadModule],
  controllers: [EQuestionsController],
  providers: [EQuestionsService],
  exports: [EQuestionsService],
})
export class EQuestionsModule {}
