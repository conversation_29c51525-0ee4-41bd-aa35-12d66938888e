import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import { EResponsesService } from './responses.service';

import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { CreateResponseDto } from '../../dto/creates/create-response.dto';
import { UpdateResponseDto } from '../../dto/updates/update-response.dto';

@ApiTags('Evaluate-Responses')
@Controller('evaluate/responses')
export class EResponsesController {
  constructor(private readonly responsesService: EResponsesService) {}

  @Post()
  create(@Body() createResponseDto: CreateResponseDto) {
    return this.responsesService.create(createResponseDto);
  }
  @Get(':assessmentId')
  async getChartData(@Param('assessmentId', ParseIntPipe) assessmentId: number) {
    return this.responsesService.getChartData(assessmentId);
  }
  @Get('/header/:assessmentId')
  async getNumberOfResponses(
    @Param('assessmentId', ParseIntPipe) assessmentId: number,
  ): Promise<number> {
    return this.responsesService.getNumberOfResponses(assessmentId);
  }
  @Get()
  findAll() {
    return this.responsesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.responsesService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateResponseDto: UpdateResponseDto,
  ) {
    return this.responsesService.update(+id, updateResponseDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.responsesService.remove(+id);
  }
}
