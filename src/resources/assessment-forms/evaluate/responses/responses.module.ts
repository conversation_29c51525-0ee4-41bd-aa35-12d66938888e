import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EResponsesService } from './responses.service';
import { EResponsesController } from './responses.controller';
import { Response } from '../../entities/response.entity';
import { Assessment } from '../../entities/assessment.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Response, Assessment])],
  controllers: [EResponsesController],
  providers: [EResponsesService],
  exports: [EResponsesService],
})
export class EResponsesModule {}
