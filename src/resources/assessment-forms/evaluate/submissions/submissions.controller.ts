import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { ESubmissionsService } from './submissions.service';
import { CreateSubmissionDto } from '../../dto/creates/create-submission.dto';
import { UpdateSubmissionDto } from '../../dto/updates/update-submission.dto';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Evaluate-Submissions')
@Controller('evaluate/submissions')
export class ESubmissionsController {
  constructor(private readonly submissionsService: ESubmissionsService) {}

  @Post()
  create(@Body() createSubmissionDto: CreateSubmissionDto) {
    return this.submissionsService.create(createSubmissionDto);
  }

  @Get()
  findAll() {
    return this.submissionsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.submissionsService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateSubmissionDto: UpdateSubmissionDto,
  ) {
    return this.submissionsService.update(+id, updateSubmissionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.submissionsService.remove(+id);
  }
}
