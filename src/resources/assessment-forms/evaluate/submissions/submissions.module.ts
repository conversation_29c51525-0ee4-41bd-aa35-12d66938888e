import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Submission } from '../../entities/submission.entity';
import { ESubmissionsController } from '../../evaluate/submissions/submissions.controller';
import { ESubmissionsService } from './submissions.service';

@Module({
  imports: [TypeOrmModule.forFeature([Submission])],
  controllers: [ESubmissionsController],
  providers: [ESubmissionsService],
  exports: [ESubmissionsService],
})
export class ESubmissionsModule {}
