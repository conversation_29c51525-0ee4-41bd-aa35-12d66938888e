import { Injectable } from '@nestjs/common';
import { CreateSubmissionDto } from '../../dto/creates/create-submission.dto';
import { UpdateSubmissionDto } from '../../dto/updates/update-submission.dto';

@Injectable()
export class ESubmissionsService {
  create(createSubmissionDto: CreateSubmissionDto) {
    return 'This action adds a new submission';
  }

  findAll() {
    return `This action returns all submissions`;
  }

  findOne(id: number) {
    return `This action returns a #${id} submission`;
  }

  update(id: number, updateSubmissionDto: UpdateSubmissionDto) {
    return `This action updates a #${id} submission`;
  }

  remove(id: number) {
    return `This action removes a #${id} submission`;
  }
}
