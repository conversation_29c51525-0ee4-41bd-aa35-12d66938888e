import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { QAssessmentsService } from './assessments.service';
import { CreateAssessmentDto } from '../../dto/creates/create-assessment.dto';
import { CreateBlockDto } from '../../dto/creates/create-block.dto';
import { UpdateAssessmentDto } from '../../dto/updates/update-assessment.dto';
import { SortOrder } from '../../dto/pagination-query.dto';
import { Assessment } from '../../entities/assessment.entity';
import type { DataParams, DataResponse } from 'src/types/params';

@ApiTags('แบบทดสอบ (Quiz)')
@Controller('quiz/assessments')
export class QuizAssessmentsController {
  constructor(private readonly assessmentsService: QAssessmentsService) {}

  @Post()
  @ApiOperation({
    summary: 'สร้างแบบทดสอบใหม่',
    description: 'สร้างแบบทดสอบ (Quiz) ใหม่ตาม template ที่กำหนด',
  })
  @ApiBody({ type: CreateAssessmentDto })
  create(@Body() createAssessmentDto: CreateAssessmentDto) {
    return this.assessmentsService.createOne(createAssessmentDto);
  }

  @Post('block')
  @ApiOperation({
    summary: 'สร้างบล็อกแบบทดสอบ',
    description: 'สร้างบล็อกคำถามใหม่สำหรับแบบทดสอบ',
  })
  @ApiBody({ type: CreateBlockDto })
  createBlock(@Body() createBlockDto: CreateBlockDto) {
    return this.assessmentsService.createBlock(createBlockDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all assessments with pagination, sorting, and search',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved assessments.',
  })
  @ApiResponse({ status: 400, description: 'Invalid query parameters.' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'sortby',
    required: false,
    type: String,
    description: 'Field to sort by (e.g., "name", "createdAt")',
  })
  @ApiQuery({
    name: 'orderby',
    required: false,
    enum: SortOrder,
    description: 'Sort order (asc or desc)',
    schema: {
      default: SortOrder.ASC,
    },
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search keyword (searches in assessment name)',
  })
  async getAll(@Query() query: DataParams): Promise<DataResponse<Assessment>> {
    return this.assessmentsService.getAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single assessment by ID' })
  @ApiParam({
    name: 'id',
    description: 'The ID of the assessment',
    type: Number,
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved assessment.',
    type: Assessment,
  })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<Assessment> {
    return this.assessmentsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing assessment' })
  @ApiParam({
    name: 'id',
    description: 'The ID of the assessment to update',
    type: Number,
    example: 1,
  })
  @ApiBody({ type: UpdateAssessmentDto })
  @ApiResponse({
    status: 200,
    description: 'The assessment has been successfully updated.',
    type: Assessment,
  })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAssessmentDto: UpdateAssessmentDto,
  ): Promise<Assessment> {
    return this.assessmentsService.update(id, updateAssessmentDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.assessmentsService.remove(+id);
  }

  @Get(':id/meta')
  @ApiOperation({ summary: 'ดึงข้อมูล Meta ของแบบทดสอบ' })
  @ApiParam({
    name: 'id',
    description: 'ID ของแบบทดสอบ',
    type: Number,
    example: 1,
  })
  async getMetaResponse(@Param('id', ParseIntPipe) id: number) {
    return this.assessmentsService.getMetaResponse(id);
  }

  @Get(':id/score-distribution')
  @ApiOperation({ summary: 'ดึงข้อมูลสัดส่วนคะแนนสำหรับแสดงเป็น Pie Chart' })
  @ApiParam({
    name: 'id',
    description: 'ID ของแบบทดสอบ',
    type: Number,
    example: 1,
  })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  async getScoreDistribution(@Param('id', ParseIntPipe) id: number) {
    return this.assessmentsService.getScoreDistribution(id);
  }

  @Get(':id/response')
  @ApiOperation({ summary: 'ดึงข้อมูลการตอบของแต่ละคำถามและตัวเลือก' })
  @ApiParam({
    name: 'id',
    description: 'ID ของแบบทดสอบ',
    type: Number,
    example: 1,
  })
  async getAllResponses(@Param('id', ParseIntPipe) id: number) {
    return this.assessmentsService.getAllResponses(id);
  }

  @Get(':id/participants')
  @ApiOperation({ summary: 'ดึงข้อมูลผู้เข้าร่วมแบบทดสอบทั้งหมด' })
  @ApiParam({
    name: 'id',
    description: 'ID ของแบบทดสอบ',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'หน้าที่ต้องการ (เริ่มจาก 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'จำนวนรายการต่อหน้า',
    example: 10,
  })
  async getAllParticipants(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: DataParams,
  ) {
    return this.assessmentsService.getAllParticipant(id, query);
  }

  @Get('participant/:id')
  @ApiOperation({
    summary: 'ดึงข้อมูลผู้เข้าร่วมแบบทดสอบรายบุคคลและผลการตอบคำถาม',
    description:
      'ดึงข้อมูลผู้เข้าร่วมแบบทดสอบรายบุคคลพร้อมประวัติการตอบคำถามทั้งหมด เปรียบเสมือนดูกระดาษคำตอบของผู้เข้าร่วมคนนั้น',
  })
  @ApiParam({
    name: 'id',
    description: 'ID ของ submission (การส่งคำตอบของผู้เข้าร่วม)',
    type: Number,
    example: 1,
  })
  @ApiResponse({ status: 404, description: 'ไม่พบข้อมูลผู้เข้าร่วมที่ระบุ' })
  async getOneParticipant(@Param('id', ParseIntPipe) id: number) {
    return this.assessmentsService.getOneParticipant(id);
  }
}
