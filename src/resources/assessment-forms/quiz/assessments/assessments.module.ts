import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QAssessmentsService } from './assessments.service';
import { QuizAssessmentsController } from './assessments.controller';
import { Assessment } from '../../entities/assessment.entity';
import { Submission } from '../../entities/submission.entity';
import { QItemBlocksModule } from '../item-blocks/item-blocks.module';
import { AssessmentValidator } from './helper/assessments.validator';
import { QHeaderBodiesModule } from '../header-bodies/header-bodies.module';
import { AssessmentHelper } from './helper/assessments.helper';
import { QSubmissionsModule } from '../submissions/submissions.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Assessment, Submission]),
    QItemBlocksModule,
    QHeaderBodiesModule,
    QSubmissionsModule,
  ],
  controllers: [QuizAssessmentsController],
  providers: [QAssessmentsService, AssessmentValidator, AssessmentHelper],
  exports: [QAssessmentsService, AssessmentValidator, AssessmentHelper],
})
export class QuizAssessmentsModule {}
