import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { CreateAssessmentDto } from '../../dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from '../../dto/updates/update-assessment.dto';
import { CreateBlockDto } from '../../dto/creates/create-block.dto';
import { Assessment } from '../../entities/assessment.entity';
import { Submission } from '../../entities/submission.entity';
import { Program } from '../../../programs/entities/program.entity';
import { User } from 'src/resources/users/entities/user.entity';
import { QItemBlocksService } from '../item-blocks/item-blocks.service';
import { AssessmentValidator } from './helper/assessments.validator';
import { ItemBlockType } from '../../enums/item-block-type.enum';
import { QHeaderBodiesService } from '../header-bodies/header-bodies.service';
import { AssessmentConfig } from 'src/configs/assessment.config';
import type { DataParams, DataResponse } from 'src/types/params';
import { AssessmentType } from '../../enums/assessment-type.enum';
import { AssessmentHelper } from './helper/assessments.helper';
import { QSubmissionsService } from '../submissions/submissions.service';

@Injectable()
export class QAssessmentsService {
  constructor(
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
    private itemBlocksService: QItemBlocksService,
    private assessmentValidator: AssessmentValidator,
    private headerBodiesService: QHeaderBodiesService,
    private assessmentHelper: AssessmentHelper,
    private submissionsService: QSubmissionsService,
  ) {}

  async createOne(dto: CreateAssessmentDto): Promise<Assessment> {
    const { creatorUserId, programId, type } = dto;

    const saved = await this.assessmentRepository.save({
      name: AssessmentConfig.FORM_UNTITLED,
      type,
      linkURL: uuidv4(),
      creator: { id: creatorUserId } as User,
      program: { id: programId } as Program,
    });

    const firstItemBlock = await this.itemBlocksService
      .createEmptyBlock({
        sequence: 1,
        section: 1,
        type: ItemBlockType.HEADER,
        assessmentId: saved.id,
      })
      .then(async (block) => {
        await this.headerBodiesService.createHeaderBody(block.id);
        return block;
      });

    // Return all required Assessment properties, filling missing ones with null or empty as needed
    return {
      ...saved,
      creator: saved.creator, // likely just id
      program: saved.program, // likely just id
      itemBlocks: [firstItemBlock],
      submissions: [], // default empty array
      isPrototype: saved.isPrototype ?? false, // fallback if not present
    } as Assessment;
  }

  async createBlock(dto: CreateBlockDto) {
    const { assessmentId, type } = dto;
    await this.assessmentValidator.validateAssessment(assessmentId);

    switch (type) {
      case ItemBlockType.HEADER:
        return this.itemBlocksService.createHeaderTemplate(assessmentId);
      case ItemBlockType.RADIO:
        return this.itemBlocksService.createQuestionTemplate(assessmentId);
      case ItemBlockType.IMAGE:
        break;
      default:
        throw new BadRequestException(`Invalid block type: ${type}`);
    }
  }

  async getAll(query: DataParams): Promise<DataResponse<Assessment>> {
    const { page, limit, sortBy, order, search } = query;

    // Use QueryBuilder for type-safe LIKE and filtering
    const qb = this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.creator', 'creator')
      .where('assessment.type = :type', { type: AssessmentType.QUIZ });

    if (search) {
      qb.andWhere('LOWER(assessment.name) LIKE LOWER(:search)', {
        search: `%${search}%`,
      });
    }

    if (sortBy) {
      qb.orderBy(
        `assessment.${sortBy}`,
        (order || 'ASC').toUpperCase() as 'ASC' | 'DESC',
      );
    } else {
      qb.orderBy('assessment.id', 'ASC');
    }

    qb.skip((page - 1) * limit).take(limit);

    const [data, total] = await qb.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      curPage: page,
      hasPrev: page > 1,
      hasNext: page < totalPages,
    };
  }

  async findOne(id: number): Promise<Assessment> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
      // relations: ['creator', 'program', 'itemBlocks', 'submissions'],
      relations: [
        'creator',
        'program',
        'itemBlocks',
        'itemBlocks.questions',
        'itemBlocks.options',
        'itemBlocks.headerBody',
        'itemBlocks.imageBody',
      ],
    });

    if (!assessment) {
      throw new NotFoundException(`Assessment with ID "${id}" not found`);
    }
    return assessment;
  }

  // FIXME: using update block instead of update assessment
  async update(
    id: number,
    updateAssessmentDto: UpdateAssessmentDto,
  ): Promise<Assessment> {
    if (!updateAssessmentDto || Object.keys(updateAssessmentDto).length === 0) {
      throw new BadRequestException('No update values provided');
    }
    const assessment = await this.findOne(id);
    const updatedAssessment = this.assessmentRepository.merge(
      assessment,
      updateAssessmentDto,
    );
    return this.assessmentRepository.save(updatedAssessment);
  }

  async remove(id: number) {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
    });

    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }

    await this.assessmentRepository.remove(assessment);
    return { success: true };
  }

  /*
    response & meta response
  */
  async getMetaResponse(assessmentId: number) {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId },
      select: ['name'],
    });

    return this.assessmentHelper.generateAssessmentMeta(
      assessmentId,
      assessment?.name,
    );
  }

  async getScoreDistribution(assessmentId: number) {
    await this.assessmentValidator.validateAssessment(assessmentId);

    return this.assessmentHelper.generateScoreDistributionChart(assessmentId);
  }

  async getAllResponses(assessmentId: number) {
    await this.assessmentValidator.validateAssessment(assessmentId);

    return this.assessmentHelper.generateAllQuestionResponses(assessmentId);
  }

  async getAllParticipant(assessmentId: number, query: DataParams) {
    await this.assessmentValidator.validateAssessment(assessmentId);

    return this.submissionsService.getAllParticipants(assessmentId, query);
  }

  async getOneParticipant(submissionId: number) {
    const participantData =
      await this.submissionsService.getOneParticipant(submissionId);

    if (!participantData) {
      throw new NotFoundException(
        `Participant with submission ID "${submissionId}" not found`,
      );
    }

    return participantData;
  }
}
