import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Assessment } from '../../../entities/assessment.entity';
import { Submission } from '../../../entities/submission.entity';
import type { DataParams, DataResponse } from 'src/types/params';

export interface AssessmentMeta {
  assessmentName: string;
  uniqueUsers: number;
  highestScore: number;
  lowestScore: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string[];
  }[];
}

export interface QuestionResponseData {
  questionId: number;
  orderInQuiz: number;
  questionText: string;
  questionType: string;
  options: OptionResponseData[];
  chartData: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor: string[];
    }[];
  };
}

export interface OptionResponseData {
  optionId: number;
  orderInQuestion: number;
  optionText: string;
  selectionCount: number;
  isCorrectAnswer: boolean;
}

@Injectable()
export class AssessmentHelper {
  constructor(
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
  ) {}

  async generateAssessmentMeta(
    assessmentId: number,
    assessmentName?: string,
  ): Promise<AssessmentMeta> {
    const uniqueUsers = await this.getUniqueUsersCount(assessmentId);
    const scoreStats = await this.getScoreStatistics(assessmentId);

    return {
      assessmentName: assessmentName || `Assessment #${assessmentId}`,
      uniqueUsers,
      highestScore: scoreStats.highestScore,
      lowestScore: scoreStats.lowestScore,
    };
  }

  async generateScoreDistributionChart(
    assessmentId: number,
  ): Promise<ChartData> {
    const submissionCount = await this.getSubmissionCount(assessmentId);

    if (submissionCount === 0) {
      return this.createEmptyChartData();
    }

    const scoreDistribution = await this.getScoreDistribution(assessmentId);
    return this.formatChartData(scoreDistribution);
  }

  /**
   * Generates all question responses data for an assessment
   */
  async generateAllQuestionResponses(
    assessmentId: number,
  ): Promise<QuestionResponseData[]> {
    const questionsData = await this.getQuestionsWithOptions(assessmentId);

    if (questionsData.length === 0) {
      return [];
    }

    const questionResponsePromises = questionsData.map(async (question) => {
      const optionsWithCounts = await this.getOptionsWithSelectionCounts(
        question.question_id,
      );

      const chartData = this.generateChartDataForQuestion(optionsWithCounts);

      return {
        questionId: question.question_id,
        orderInQuiz: question.order_in_quiz,
        questionText: question.question_text,
        questionType: question.question_type,
        options: optionsWithCounts,
        chartData: chartData,
      };
    });

    return Promise.all(questionResponsePromises);
  }

  /**
   * Private functions 🔒
   */

  private async getUniqueUsersCount(assessmentId: number): Promise<number> {
    const result = await this.submissionRepository
      .createQueryBuilder('submission')
      .select('COUNT(DISTINCT submission.userId)', 'uniqueUsers')
      .where('submission.assessmentId = :assessmentId', { assessmentId })
      .getRawOne();

    return parseInt(result?.uniqueUsers || '0');
  }

  private async getScoreStatistics(assessmentId: number): Promise<{
    highestScore: number;
    lowestScore: number;
  }> {
    const result = await this.submissionRepository
      .createQueryBuilder()
      .select([
        'MAX(total_score) as highestScore',
        'MIN(CASE WHEN total_score > 0 THEN total_score END) as lowestScore',
      ])
      .from((subQuery) => {
        return subQuery
          .select([
            'submission.id',
            'COALESCE(SUM(option.value), 0) as total_score',
          ])
          .from('submission', 'submission')
          .leftJoin('submission.responses', 'response')
          .leftJoin('response.selectedOption', 'option')
          .where('submission.assessmentId = :assessmentId', { assessmentId })
          .groupBy('submission.id');
      }, 'submission_scores')
      .getRawOne();

    return {
      highestScore: parseFloat(result?.highestScore || '0'),
      lowestScore: parseFloat(result?.lowestScore || '0'),
    };
  }

  private async getSubmissionCount(assessmentId: number): Promise<number> {
    return await this.submissionRepository.count({
      where: { assessmentId },
    });
  }

  private createEmptyChartData(): ChartData {
    return {
      labels: ['No Data'],
      datasets: [
        {
          data: [1],
          backgroundColor: ['#e0e0e0'],
        },
      ],
    };
  }

  private async getScoreDistribution(assessmentId: number): Promise<any[]> {
    return await this.submissionRepository
      .createQueryBuilder()
      .select([
        `CASE 
          WHEN score_percentage <= 20 THEN '0-20'
          WHEN score_percentage <= 40 THEN '21-40'
          WHEN score_percentage <= 60 THEN '41-60'
          WHEN score_percentage <= 80 THEN '61-80'
          ELSE '81-100'
        END as scoreRange`,
        'COUNT(*) as count',
      ])
      .from((subQuery) => {
        return subQuery
          .select([
            'submission.id',
            'COALESCE(SUM(option.value), 0) * 100 as score_percentage',
          ])
          .from('submission', 'submission')
          .leftJoin('submission.responses', 'response')
          .leftJoin('response.selectedOption', 'option')
          .where('submission.assessmentId = :assessmentId', { assessmentId })
          .groupBy('submission.id');
      }, 'scores')
      .groupBy(
        `CASE 
        WHEN score_percentage <= 20 THEN '0-20'
        WHEN score_percentage <= 40 THEN '21-40'
        WHEN score_percentage <= 60 THEN '41-60'
        WHEN score_percentage <= 80 THEN '61-80'
        ELSE '81-100'
      END`,
      )
      .getRawMany();
  }

  private formatChartData(scoreDistribution: any[]): ChartData {
    const scoreRanges = this.initializeScoreRanges();
    this.populateScoreRanges(scoreRanges, scoreDistribution);

    return {
      labels: Object.keys(scoreRanges),
      datasets: [
        {
          data: Object.values(scoreRanges).map((range) => range.count),
          backgroundColor: Object.values(scoreRanges).map(
            (range) => range.color,
          ),
        },
      ],
    };
  }

  private initializeScoreRanges() {
    return {
      '0-20': { count: 0, color: '#FF6384', hoverColor: '#FF4E75' },
      '21-40': { count: 0, color: '#FFCE56', hoverColor: '#FFB916' },
      '41-60': { count: 0, color: '#36A2EB', hoverColor: '#1A8FE3' },
      '61-80': { count: 0, color: '#4BC0C0', hoverColor: '#2EA7A7' },
      '81-100': { count: 0, color: '#9966FF', hoverColor: '#7F40FF' },
    };
  }

  private populateScoreRanges(
    scoreRanges: any,
    scoreDistribution: any[],
  ): void {
    scoreDistribution.forEach((result) => {
      const range = result.scoreRange;
      if (scoreRanges[range]) {
        scoreRanges[range].count = parseInt(result.count);
      }
    });
  }

  /**
   * Private functions 👇
   */

  /**
   * Gets all questions with their basic information for an assessment
   */
  private async getQuestionsWithOptions(assessmentId: number): Promise<any[]> {
    return await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.assessment', 'assessment')
      .leftJoin('assessment.itemBlocks', 'itemBlock')
      .leftJoin('itemBlock.questions', 'question')
      .select([
        'DISTINCT question.id as question_id',
        'itemBlock.sequence as order_in_quiz',
        'question.questionText as question_text',
        'itemBlock.type as question_type',
      ])
      .where('submission.assessmentId = :assessmentId', { assessmentId })
      .andWhere('question.id IS NOT NULL')
      .orderBy('itemBlock.sequence', 'ASC')
      .getRawMany();
  }

  /**
   * Gets options with selection counts for a specific question
   */
  private async getOptionsWithSelectionCounts(
    questionId: number,
  ): Promise<OptionResponseData[]> {
    return await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.responses', 'response')
      .leftJoin('response.selectedOption', 'option')
      .leftJoin('response.question', 'question')
      .leftJoin('question.itemBlock', 'itemBlock')
      .select([
        'option.id as option_id',
        'option.sequence as order_in_question',
        'option.optionText as option_text',
        'COUNT(response.selectedOptionId) as selection_count',
        'CASE WHEN option.value > 0 THEN true ELSE false END as is_correct_answer',
      ])
      .where('question.id = :questionId', { questionId })
      .andWhere('option.id IS NOT NULL')
      .groupBy('option.id')
      .orderBy('option.sequence', 'ASC')
      .getRawMany()
      .then((results) =>
        results.map((result) => ({
          optionId: parseInt(result.option_id),
          orderInQuestion: parseInt(result.order_in_question),
          optionText: result.option_text,
          selectionCount: parseInt(result.selection_count),
          isCorrectAnswer:
            result.is_correct_answer === 'true' ||
            result.is_correct_answer === true,
        })),
      );
  }

  /**
   * Generates chart data for a question that can be used for both pie and bar charts
   */
  private generateChartDataForQuestion(
    optionsWithCounts: OptionResponseData[],
  ): ChartData {
    const labels = optionsWithCounts.map((option) => option.optionText);
    const data = optionsWithCounts.map((option) => option.selectionCount);

    const backgroundColor = this.generateChartColors(
      labels.length,
    ).backgroundColor;

    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor,
        },
      ],
    };
  }

  private generateChartColors(count: number): {
    backgroundColor: string[];
  } {
    const colorPairs = [
      { bg: '#FF6384', hover: '#FF4E75' },
      { bg: '#36A2EB', hover: '#1A8FE3' },
      { bg: '#FFCE56', hover: '#FFB916' },
      { bg: '#4BC0C0', hover: '#2EA7A7' },
      { bg: '#9966FF', hover: '#7F40FF' },
      { bg: '#FF9F40', hover: '#FF8B20' },
      { bg: '#8C9EFF', hover: '#5870FF' },
      { bg: '#FF5252', hover: '#FF3838' },
      { bg: '#69F0AE', hover: '#3DE990' },
      { bg: '#FFD740', hover: '#FFCB10' },
    ];

    const backgroundColor: string[] = [];

    for (let i = 0; i < count; i++) {
      const pair = colorPairs[i % colorPairs.length];
      backgroundColor.push(pair.bg);
    }

    return { backgroundColor };
  }
}
