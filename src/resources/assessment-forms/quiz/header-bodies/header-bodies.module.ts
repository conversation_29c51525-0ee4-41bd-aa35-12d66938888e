import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QHeaderBodiesService } from './header-bodies.service';
import { HeaderBodiesController } from './header-bodies.controller';
import { HeaderBody } from '../../entities/header-body.entity';
import { HeaderBodiesHelper } from './helper/header-bodies.helper';

@Module({
  imports: [TypeOrmModule.forFeature([HeaderBody])],
  controllers: [HeaderBodiesController],
  providers: [QHeaderBodiesService, HeaderBodiesHelper],
  exports: [QHeaderBodiesService, HeaderBodiesHelper],
})
export class QHeaderBodiesModule {}
