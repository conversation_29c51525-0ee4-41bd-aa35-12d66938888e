import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HeaderBody } from '../../entities/header-body.entity';
import { AssessmentConfig } from 'src/configs/assessment.config';

@Injectable()
export class QHeaderBodiesService {
  constructor(
    @InjectRepository(HeaderBody)
    private headerBodyRepository: Repository<HeaderBody>,
  ) {}

  async createHeaderBody(itemBlockId: number): Promise<HeaderBody> {
    const headerBody = this.headerBodyRepository.create({
      title: AssessmentConfig.DEFAULT_HEADER_TITLE,
      description: AssessmentConfig.DEFAULT_HEADER_DESCRIPTION,
      itemBlock: { id: itemBlockId },
    });

    return await this.headerBodyRepository.save(headerBody);
  }
}
