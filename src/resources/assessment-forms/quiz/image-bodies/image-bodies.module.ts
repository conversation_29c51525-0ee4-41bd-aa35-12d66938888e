import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QImageBodiesService } from './image-bodies.service';
import { ImageBodiesController } from './image-bodies.controller';
import { ImageBody } from '../../entities/image-body.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ImageBody])],
  controllers: [ImageBodiesController],
  providers: [QImageBodiesService],
  exports: [QImageBodiesService],
})
export class QImageBodiesModule {}
