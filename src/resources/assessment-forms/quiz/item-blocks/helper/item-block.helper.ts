import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ItemBlock } from 'src/resources/assessment-forms/entities/item-block.entity';
import { Repository } from 'typeorm';

@Injectable()
export class ItemBlockHelper {
  constructor(
    @InjectRepository(ItemBlock)
    private itemBlockRepository: Repository<ItemBlock>,
  ) {}

  async getMaxSequence(assessmentId: number): Promise<number> {
    const lastItemBlock = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.sequence', 'DESC')
      .getOne();

    return lastItemBlock ? lastItemBlock.sequence + 1 : 1;
  }

  async getMaxSectionNumber(assessmentId: number): Promise<number> {
    const lastSection = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.section', 'DESC')
      .getOne();

    return lastSection ? lastSection.section + 1 : 1;
  }

  async getItemBlockBySequence(
    assessmentId: number,
    sequence: number,
  ): Promise<ItemBlock | null> {
    return await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .andWhere('itemBlock.sequence = :sequence', { sequence })
      .getOne();
  }

  async reorderSequences(assessmentId: number): Promise<void> {
    const itemBlocks = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .orderBy('itemBlock.sequence', 'ASC')
      .getMany();

    for (let i = 0; i < itemBlocks.length; i++) {
      await this.itemBlockRepository.update(itemBlocks[i].id, {
        sequence: i + 1,
      });
    }
  }

  async validateSequence(
    assessmentId: number,
    sequence: number,
  ): Promise<boolean> {
    const existingBlock = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .where('itemBlock.assessmentId = :assessmentId', { assessmentId })
      .andWhere('itemBlock.sequence = :sequence', { sequence })
      .getOne();

    return !existingBlock;
  }

  async duplicateItemBlock(
    sourceItemBlockId: number,
    assessmentId: number,
  ): Promise<ItemBlock> {
    const sourceBlock = await this.itemBlockRepository
      .createQueryBuilder('itemBlock')
      .leftJoinAndSelect('itemBlock.questions', 'questions')
      .leftJoinAndSelect('itemBlock.options', 'options')
      .leftJoinAndSelect('itemBlock.headerBody', 'headerBody')
      .where('itemBlock.id = :id', { id: sourceItemBlockId })
      .getOne();

    if (!sourceBlock) {
      throw new Error('Source item block not found');
    }

    const nextSequence = await this.getMaxSequence(assessmentId);

    const duplicatedBlock = this.itemBlockRepository.create({
      sequence: nextSequence,
      section: sourceBlock.section,
      type: sourceBlock.type,
      isRequired: sourceBlock.isRequired,
      assessment: { id: assessmentId },
    });

    return await this.itemBlockRepository.save(duplicatedBlock);
  }
}
