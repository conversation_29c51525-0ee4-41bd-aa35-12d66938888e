import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { QItemBlocksService } from './item-blocks.service';
import { CreateItemBlockDto } from '../../dto/creates/create-item-block.dto';
import { UpdateItemBlockDto } from '../../dto/updates/update-item-block.dto';
import { UpdateItemBlockSequencesDto } from '../../dto/updates/ีupdate-block-sequence.dto';
import { ApiProperty } from '@nestjs/swagger';

@Controller('quiz/item-blocks')
export class ItemBlocksController {
  constructor(private readonly itemBlocksService: QItemBlocksService) {}

  @Post()
  create(@Body() createItemBlockDto: CreateItemBlockDto) {
    return this.itemBlocksService.create(createItemBlockDto);
  }

  @Get()
  findAll() {
    return this.itemBlocksService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.itemBlocksService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateItemBlockDto: UpdateItemBlockDto,
  ) {
    return this.itemBlocksService.update(+id, updateItemBlockDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.itemBlocksService.remove(+id);
  }

  @Post('update-sequences')
  updateSequences(@Body() updateSequencesDto: UpdateItemBlockSequencesDto) {
    return this.itemBlocksService.updateSequences(updateSequencesDto);
  }
}
