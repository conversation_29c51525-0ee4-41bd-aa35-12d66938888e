import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QItemBlocksService } from './item-blocks.service';
import { ItemBlocksController } from './item-blocks.controller';
import { ItemBlock } from '../../entities/item-block.entity';
import { Question } from '../../entities/question.entity';
import { Option } from '../../entities/option.entity';
import { HeaderBody } from '../../entities/header-body.entity';
import { QHeaderBodiesModule } from '../header-bodies/header-bodies.module';
import { ItemBlockHelper } from './helper/item-block.helper';
import { QQuestionsModule } from '../questions/questions.module';
import { QOptionsModule } from '../options/options.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ItemBlock, Question, Option, HeaderBody]),
    QHeaderBodiesModule,
    QQuestionsModule,
    QOptionsModule,
  ],
  controllers: [ItemBlocksController],
  providers: [QItemBlocksService, ItemBlockHelper],
  exports: [QItemBlocksService, ItemBlockHelper],
})
export class QItemBlocksModule {}
