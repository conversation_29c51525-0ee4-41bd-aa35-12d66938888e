import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { ItemBlock } from '../../entities/item-block.entity';
import { Question } from '../../entities/question.entity';
import { Option } from '../../entities/option.entity';
import { HeaderBody } from '../../entities/header-body.entity';
import { ItemBlockType } from '../../enums/item-block-type.enum';
import { CreateItemBlockDto } from '../../dto/creates/create-item-block.dto';
import { UpdateItemBlockDto } from '../../dto/updates/update-item-block.dto';
import { QHeaderBodiesService } from '../header-bodies/header-bodies.service';
import { ItemBlockHelper } from './helper/item-block.helper';
import { QQuestionsService } from '../questions/questions.service';
import { QOptionsService } from '../options/options.service';
import { UpdateItemBlockSequencesDto } from '../../dto/updates/ีupdate-block-sequence.dto';

@Injectable()
export class QItemBlocksService {
  constructor(
    @InjectRepository(ItemBlock)
    public itemBlockRepository: Repository<ItemBlock>, // เปลี่ยนเป็น public เล่อให้ services อื่นสามารถเข้าได้
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
    @InjectRepository(Option)
    private optionRepository: Repository<Option>,
    private readonly headerBodiesService: QHeaderBodiesService,
    private readonly itemBlockHelper: ItemBlockHelper,
    private readonly questionService: QQuestionsService,
    private readonly optionsService: QOptionsService,
  ) {}

  create(dto: CreateItemBlockDto) {
    return this.itemBlockRepository.save({ ...dto });
  }

  findAll() {
    return `This action returns all itemBlocks`;
  }

  findOne(id: number) {
    return `This action returns a #${id} itemBlock`;
  }

  update(id: number, updateItemBlockDto: UpdateItemBlockDto) {
    return `This action updates a #${id} itemBlock`;
  }

  remove(id: number) {
    return `This action removes a #${id} itemBlock`;
  }

  async createHeaderTemplate(assessmentId: number) {
    const nextSequence =
      await this.itemBlockHelper.getMaxSequence(assessmentId);
    const nextSection =
      await this.itemBlockHelper.getMaxSectionNumber(assessmentId);

    const { block: savedHeaderBlock, body: savedHeaderBody } =
      await this.createHeaderBlockWithBody(
        assessmentId,
        nextSequence,
        nextSection,
      );
    const nextSequence2 =
      await this.itemBlockHelper.getMaxSequence(assessmentId);
    const nextSection2 =
      await this.itemBlockHelper.getMaxSectionNumber(assessmentId);

    const { block: savedHeaderBlock2, body: savedHeaderBody2 } =
      await this.createHeaderBlockWithBody(
        assessmentId,
        nextSequence2,
        nextSection2,
      );

    return {
      id: savedHeaderBlock.id,
      sequence: savedHeaderBlock.sequence,
      section: savedHeaderBlock.section,
      type: savedHeaderBlock.type,
      headerBody: {
        id: savedHeaderBody.id,
        title: savedHeaderBody.title,
        description: savedHeaderBody.description,
      },
    };
  }

  async createQuestionTemplate(assessmentId: number) {
    const nextSequence =
      await this.itemBlockHelper.getMaxSequence(assessmentId);
    const nextSection =
      await this.itemBlockHelper.getMaxSectionNumber(assessmentId);

    const { block: savedQuestionBlock, body: savedQuestionBody } =
      await this.createQuestionBlockWithBody(
        assessmentId,
        nextSequence,
        nextSection,
      );

    const { body: savedOptionBlock } = await this.createOption(
      savedQuestionBlock.id,
    );

    return {
      id: savedQuestionBlock.id,
      sequence: savedQuestionBlock.sequence,
      section: savedQuestionBlock.section,
      type: savedQuestionBlock.type,
      isRequired: savedQuestionBlock.isRequired,
      question: {
        id: savedQuestionBody.id,
        questionText: savedQuestionBody.questionText,
        imagePath: savedQuestionBody.imagePath,
        isHeader: savedQuestionBody.isHeader,
        sequence: savedQuestionBody.sequence,
        sizeLimit: savedQuestionBody.sizeLimit,
        acceptFile: savedQuestionBody.acceptFile,
        uploadLimit: savedQuestionBody.uploadLimit,
      },
      options: [
        {
          id: savedOptionBlock.id,
          optionText: savedOptionBlock.optionText,
          imagePath: savedOptionBlock.imagePath,
          value: savedOptionBlock.value,
          sequence: savedOptionBlock.sequence,
          nextSection: savedOptionBlock.nextSection,
        },
      ],
    };
  }

  async createEmptyBlock({
    assessmentId,
    sequence,
    section,
    type,
  }: {
    assessmentId: number;
    sequence?: number;
    section?: number;
    type?: ItemBlockType;
  }): Promise<ItemBlock> {
    //this method will create a block with no body
    const block = this.itemBlockRepository.create({
      sequence,
      section,
      type: type,
      isRequired: false,
      assessment: { id: assessmentId },
    });

    return await this.itemBlockRepository.save(block);
  }

  private async createHeaderBlockWithBody(
    assessmentId: number,
    sequence: number,
    section: number,
  ) {
    const headerBlock = await this.createEmptyBlock({
      assessmentId,
      sequence,
      section,
      type: ItemBlockType.HEADER,
    });

    const headerBody = await this.headerBodiesService.createHeaderBody(
      headerBlock.id,
    );

    return {
      block: headerBlock,
      body: headerBody,
    };
  }

  private async createQuestionBlockWithBody(
    assessmentId: number,
    sequence: number,
    section: number,
  ) {
    const questionBlock = await this.createEmptyBlock({
      assessmentId,
      sequence,
      section,
      type: ItemBlockType.RADIO,
    });

    const questionBody = await this.questionService.createQuestion(
      questionBlock.id,
    );

    return {
      block: questionBlock,
      body: questionBody,
    };
  }

  private async createOption(blockId: number) {
    const option = await this.optionsService.createOption(blockId);

    return {
      body: option,
    };
  }

  /**
   * Updates the sequence of multiple item blocks simultaneously using a safe and optimized bulk update approach
   * @param updateSequencesDto DTO containing an array of item blocks with their new sequence numbers
   * @returns Object with success status and updated item blocks
   */
  async updateSequences(updateSequencesDto: UpdateItemBlockSequencesDto) {
    const { itemBlocks } = updateSequencesDto;

    if (!itemBlocks || itemBlocks.length === 0) {
      throw new BadRequestException(
        'No item blocks provided for sequence update',
      );
    }

    const itemBlockIds = itemBlocks.map((item) => item.id);

    const queryRunner =
      this.itemBlockRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const existingBlocks = await this.itemBlockRepository.find({
        where: { id: In(itemBlockIds) },
        select: ['id', 'sequence', 'assessmentId'],
      });

      if (existingBlocks.length !== itemBlockIds.length) {
        const foundIds = existingBlocks.map((block) => block.id);
        const missingIds = itemBlockIds.filter((id) => !foundIds.includes(id));
        throw new NotFoundException(
          `Item blocks with IDs ${missingIds.join(', ')} not found`,
        );
      }

      const caseStatements = itemBlocks
        .map(() => `WHEN id = ? THEN ?`)
        .join(' ');

      const wherePlaceholders = itemBlocks.map(() => `?`).join(',');

      // สร้าง params: [id1, seq1, id2, seq2, ..., idN, seqN, id1, id2, ..., idN]
      const params: any[] = [];
      itemBlocks.forEach((item) => {
        params.push(item.id, item.sequence);
      });
      itemBlocks.forEach((item) => {
        params.push(item.id);
      });

      const rawQuery = `
  UPDATE item_blocks
  SET sequence = CASE ${caseStatements} ELSE sequence END
  WHERE id IN (${wherePlaceholders})
`;

      await queryRunner.manager.query(rawQuery, params);

      await queryRunner.commitTransaction();

      const updatedBlocks = await this.itemBlockRepository.find({
        where: { id: In(itemBlockIds) },
      });

      return {
        success: true,
        message: 'Item block sequences updated successfully',
        data: updatedBlocks.map((block) => ({
          id: block.id,
          sequence: block.sequence,
        })),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      throw new InternalServerErrorException(
        `Failed to update item block sequences: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }
}
