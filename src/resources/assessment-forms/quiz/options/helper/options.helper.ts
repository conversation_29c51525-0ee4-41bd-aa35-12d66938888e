import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Option } from 'src/resources/assessment-forms/entities/option.entity';

@Injectable()
export class OptionsHelper {
  constructor(
    @InjectRepository(Option)
    private optionRepository: Repository<Option>,
  ) {}

  async getMaxSequence(itemBlockId: number): Promise<number> {
    const lastOption = await this.optionRepository
      .createQueryBuilder('option')
      .where('option.itemBlockId = :itemBlockId', { itemBlockId })
      .orderBy('option.sequence', 'DESC')
      .getOne();

    return lastOption ? lastOption.sequence + 1 : 1;
  }
}
