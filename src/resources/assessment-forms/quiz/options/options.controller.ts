import { Controller } from '@nestjs/common';
import { QOptionsService } from './options.service';
import { Post, Param, ParseIntPipe } from '@nestjs/common';
import { Option } from '../../entities/option.entity';
import { ApiTags } from '@nestjs/swagger';
import { Create } from 'sharp';
import { CreateOptionDto } from '../../dto/creates/create-option.dto';

@ApiTags('Quiz Options')
@Controller('quiz/options')
export class OptionsController {
  constructor(private readonly optionsService: QOptionsService) {}

  @Post()
  async createOption(createOptionDto: CreateOptionDto): Promise<Option> {
    const { itemBlockId } = createOptionDto;
    return this.optionsService.createOption(itemBlockId);
  }
}
