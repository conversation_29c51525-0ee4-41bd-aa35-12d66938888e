import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QOptionsService } from './options.service';
import { OptionsController } from './options.controller';
import { Option } from '../../entities/option.entity';
import { OptionsHelper } from './helper/options.helper';

@Module({
  imports: [TypeOrmModule.forFeature([Option])],
  controllers: [OptionsController],
  providers: [QOptionsService, OptionsHelper],
  exports: [QOptionsService, OptionsHelper],
})
export class QOptionsModule {}
