import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Option } from '../../entities/option.entity';
import { Repository } from 'typeorm';
import { OptionsHelper } from './helper/options.helper';

@Injectable()
export class QOptionsService {
  constructor(
    @InjectRepository(Option)
    private readonly optionRepository: Repository<Option>,
    private readonly optionsHelper: OptionsHelper,
  ) {}

  async createOption(itemBlockId: number): Promise<Option> {
    const maxSequence = await this.optionsHelper.getMaxSequence(itemBlockId);
    const newOption = this.optionRepository.create({
      itemBlock: { id: itemBlockId },
      sequence: maxSequence,
      optionText: 'Option ' + maxSequence,
    });
    return this.optionRepository.save(newOption);
  }
}
