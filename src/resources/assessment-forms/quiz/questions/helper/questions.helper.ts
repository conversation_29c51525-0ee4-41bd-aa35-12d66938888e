import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Question } from 'src/resources/assessment-forms/entities/question.entity';

@Injectable()
export class QuestionsHelper {
  constructor(
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
  ) {}

  async getMaxSequence(itemBlockId: number): Promise<number> {
    const lastQuestion = await this.questionRepository
      .createQueryBuilder('question')
      .where('question.itemBlockId = :itemBlockId', { itemBlockId })
      .orderBy('question.sequence', 'DESC')
      .getOne();

    return lastQuestion ? lastQuestion.sequence + 1 : 1;
  }
}
