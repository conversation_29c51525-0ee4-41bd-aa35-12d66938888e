import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QQuestionsService } from './questions.service';
import { QuestionsController } from './questions.controller';
import { Question } from '../../entities/question.entity';
import { QuestionsHelper } from './helper/questions.helper';

@Module({
  imports: [TypeOrmModule.forFeature([Question])],
  controllers: [QuestionsController],
  providers: [QQuestionsService, QuestionsHelper],
  exports: [QQuestionsService, QuestionsHelper],
})
export class QQuestionsModule {}
