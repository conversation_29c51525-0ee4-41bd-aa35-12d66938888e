import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Question } from '../../entities/question.entity';
import { Repository } from 'typeorm';
import { QuestionsHelper } from './helper/questions.helper';

@Injectable()
export class QQuestionsService {
  constructor(
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
    private questionsHelper: QuestionsHelper,
  ) {}

  async createQuestion(itemBlockId: number): Promise<Question> {
    const maxSequence = await this.questionsHelper.getMaxSequence(itemBlockId);
    const newQuestion = this.questionRepository.create({
      itemBlock: { id: itemBlockId },
      sequence: maxSequence,
      questionText: '',
      score: null,
    });
    return await this.questionRepository.save(newQuestion);
  }
}
