import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QResponsesService } from './responses.service';
import { ResponsesController } from './responses.controller';
import { Response } from '../../entities/response.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Response])],
  controllers: [ResponsesController],
  providers: [QResponsesService],
  exports: [QResponsesService],
})
export class QResponsesModule {}
