import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Submission } from '../../../entities/submission.entity';
import type { DataParams, DataResponse } from 'src/types/params';

export interface ParticipantData {
  id: number;
  date: string;
  userName: string;
  score: number;
}

@Injectable()
export class SubmissionsHelper {
  constructor(
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
  ) {}

  async getParticipants(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<ParticipantData>> {
    const { page, limit, sortBy, order, search } = query;

    const qb = this.submissionRepository
      .createQueryBuilder('submission')
      .select([
        'submission.id as id',
        'DATE_FORMAT(submission.startAt, "%Y-%m-%d") as date',
        'user.name as userName',
        'COALESCE(SUM(option.value), 0) as score',
      ])
      .leftJoin('submission.user', 'user')
      .leftJoin('submission.responses', 'response')
      .leftJoin('response.selectedOption', 'option')
      .where('submission.assessmentId = :assessmentId', { assessmentId });

    if (search) {
      qb.andWhere('user.name LIKE :search', { search: `%${search}%` });
    }

    qb.groupBy('submission.id');

    if (sortBy) {
      qb.orderBy(`${sortBy}`, (order || 'ASC').toUpperCase() as 'ASC' | 'DESC');
    } else {
      qb.orderBy('submission.id', 'ASC');
    }

    qb.offset((page - 1) * limit).limit(limit);

    const countQb = this.submissionRepository
      .createQueryBuilder('submission')
      .select('COUNT(DISTINCT submission.id)', 'count')
      .where('submission.assessmentId = :assessmentId', { assessmentId });

    if (search) {
      countQb
        .leftJoin('submission.user', 'user')
        .andWhere('user.name LIKE :search', { search: `%${search}%` });
    }

    const participants = await qb.getRawMany();
    const totalCount = await countQb.getRawOne();
    const total = totalCount ? parseInt(totalCount.count, 10) : 0;
    const totalPages = Math.ceil(total / limit);

    // แปลงข้อมูลให้เป็น camelCase ตามที่ต้องการ เฉพาะ id, date, userName และ score
    const formattedParticipants = participants.map((participant) => {
      const score = parseInt(participant.score, 10) || 0;

      return {
        id: participant.id,
        date: participant.date,
        userName: participant.userName,
        score: score,
      };
    });

    return {
      data: formattedParticipants,
      total,
      curPage: page,
      hasPrev: page > 1,
      hasNext: page < totalPages,
    };
  }

  async getOneParticipant(submissionId: number) {
    // 1. Get the submission with user and assessment data
    const submission = await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.user', 'user')
      .leftJoinAndSelect('submission.assessment', 'assessment')
      .where('submission.id = :submissionId', { submissionId })
      .getOne();

    if (!submission) {
      return null;
    }

    // 2. Get all responses for this submission with related data
    const responses = await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.responses', 'response')
      .leftJoinAndSelect('response.question', 'question')
      .leftJoinAndSelect('question.itemBlock', 'itemBlock')
      .leftJoinAndSelect('response.selectedOption', 'option')
      .leftJoinAndSelect('itemBlock.options', 'allOptions')
      .where('submission.id = :submissionId', { submissionId })
      .orderBy('itemBlock.sequence', 'ASC')
      .addOrderBy('question.sequence', 'ASC')
      .getOne();

    // 3. Calculate the total score
    let totalScore = 0;
    let totalPossibleScore = 0;

    if (responses && responses.responses) {
      responses.responses.forEach((response) => {
        if (response.selectedOption && response.selectedOption.value) {
          totalScore += response.selectedOption.value;
        }

        if (
          response.question &&
          response.question.itemBlock &&
          response.question.itemBlock.options
        ) {
          const maxValue = Math.max(
            ...response.question.itemBlock.options.map((opt) => opt.value || 0),
          );
          totalPossibleScore += maxValue;
        }
      });
    }

    // 4. Format the questions and answers for easy display
    const formattedQuestions =
      responses?.responses?.map((response) => {
        const question = response.question;
        const selectedOption = response.selectedOption;
        const allOptions = question.itemBlock.options;

        return {
          questionId: question.id,
          questionText: question.questionText,
          questionSequence: question.sequence,
          section: question.itemBlock.sequence,
          selectedOptionId: selectedOption?.id,
          selectedOptionText: selectedOption?.optionText,
          isCorrect: selectedOption?.value > 0,
          score: selectedOption?.value || 0,
          options: allOptions.map((option) => ({
            id: option.id,
            text: option.optionText,
            sequence: option.sequence,
            value: option.value,
            isSelected: option.id === selectedOption?.id,
          })),
        };
      }) || [];

    // 5. Return formatted response
    return {
      submissionId: submission.id,
      assessmentId: submission.assessmentId,
      assessmentName: submission.assessment.name,
      userId: submission.user.id,
      userName: submission.user.name,
      startTime: submission.startAt,
      endTime: submission.endAt,
      totalScore: totalScore,
      maxScore: totalPossibleScore,
      scorePercentage:
        totalPossibleScore > 0 ? (totalScore / totalPossibleScore) * 100 : 0,
      questions: formattedQuestions,
    };
  }
}
