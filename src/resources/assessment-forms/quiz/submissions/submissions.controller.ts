import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { QSubmissionsService } from './submissions.service';
import { CreateSubmissionDto } from '../../dto/creates/create-submission.dto';
import { UpdateSubmissionDto } from '../../dto/updates/update-submission.dto';
import { ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import type { DataParams } from 'src/types/params';

@ApiTags('แบบทดสอบ - ส่งคำตอบ (Quiz Submissions)')
@Controller('quiz/submissions')
export class QSubmissionsController {
  constructor(private readonly submissionsService: QSubmissionsService) {}

  @Post()
  create(@Body() createSubmissionDto: CreateSubmissionDto) {
    return this.submissionsService.create(createSubmissionDto);
  }

  @Get()
  findAll() {
    return this.submissionsService.findAll();
  }

  @Get('assessment/:id/participants')
  @ApiOperation({ summary: 'ดึงข้อมูลผู้เข้าร่วมแบบทดสอบทั้งหมด' })
  @ApiParam({
    name: 'id',
    description: 'ID ของแบบทดสอบ',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'หน้าที่ต้องการ (เริ่มจาก 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'จำนวนรายการต่อหน้า',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'คำค้นหา (ค้นหาจากชื่อผู้เข้าร่วม)',
  })
  async getAllParticipants(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: DataParams,
  ) {
    return this.submissionsService.getAllParticipants(id, query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.submissionsService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateSubmissionDto: UpdateSubmissionDto,
  ) {
    return this.submissionsService.update(+id, updateSubmissionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.submissionsService.remove(+id);
  }
}
