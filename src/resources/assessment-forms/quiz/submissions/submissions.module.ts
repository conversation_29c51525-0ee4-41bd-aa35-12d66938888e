import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QSubmissionsService } from './submissions.service';
import { QSubmissionsController } from './submissions.controller';
import { Submission } from '../../entities/submission.entity';
import { SubmissionsHelper } from './helper/submissions.helper';

@Module({
  imports: [TypeOrmModule.forFeature([Submission])],
  controllers: [QSubmissionsController],
  providers: [QSubmissionsService, SubmissionsHelper],
  exports: [QSubmissionsService, SubmissionsHelper],
})
export class QSubmissionsModule {}
