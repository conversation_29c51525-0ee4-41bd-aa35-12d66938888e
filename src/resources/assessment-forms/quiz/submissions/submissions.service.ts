import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateSubmissionDto } from '../../dto/creates/create-submission.dto';
import { UpdateSubmissionDto } from '../../dto/updates/update-submission.dto';
import { Submission } from '../../entities/submission.entity';
import type { DataParams, DataResponse } from 'src/types/params';
import { SubmissionsHelper } from './helper/submissions.helper';

@Injectable()
export class QSubmissionsService {
  constructor(
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
    private submissionsHelper: SubmissionsHelper,
  ) {}

  create(createSubmissionDto: CreateSubmissionDto) {
    return 'This action adds a new submission';
  }

  findAll() {
    return `This action returns all submissions`;
  }

  findOne(id: number) {
    return `This action returns a #${id} submission`;
  }

  update(id: number, updateSubmissionDto: UpdateSubmissionDto) {
    return `This action updates a #${id} submission`;
  }

  remove(id: number) {
    return `This action removes a #${id} submission`;
  }

  async getAllParticipants(assessmentId: number, query: DataParams) {
    return this.submissionsHelper.getParticipants(assessmentId, query);
  }

  async getOneParticipant(submissionId: number) {
    return this.submissionsHelper.getOneParticipant(submissionId);
  }
}
