/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { Assessment } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import { AssessmentService } from 'src/services/asm/assessmentService';

export const useEvaluateFormStore = defineStore('evaluateForm', () => {
  // 🔹 State
  const meta = ref<DataResponse<Assessment> | null>(null);
  const assessments = ref<Assessment[]>([]);
  const currentAssessment = ref<Assessment | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const page = ref(1);
  const limit = ref(5);
  const search = ref('');

  // 🔹 Actions
  const fetchAssessmentById = async (id: number) => {
    loading.value = true;
    error.value = null;
    try {
      const res = await new AssessmentService('evaluate').fetchOne(id);
      if (res) {
        currentAssessment.value = res;
      }
    } catch (err: any) {
      error.value = err?.message ?? 'ไม่สามารถโหลดแบบทดสอบได้';
    } finally {
      loading.value = false;
    }
  };

  // ! not implemented yet
  // const getAssessmentByIdWithSec = async (id: number, section: number) => {
  //   loading.value = true;
  //   error.value = null;
  //   try {
  //     const { data } = await form.getFormByIdWithSec(id, section);
  //     if (data) {
  //       currentAssessment.value = data;
  //     }
  //   } catch (err: any) {
  //     error.value = err?.message ?? 'ไม่สามารถโหลดแบบทดสอบได้';
  //   } finally {
  //     loading.value = false;
  //   }
  // };

  const addAssessment = async (assessmentData: Partial<Assessment>): Promise<Assessment> => {
    const res = await new AssessmentService('evaluate').createOne(assessmentData);
    assessments.value.push(res);
    currentAssessment.value = res;
    return res;
  };

  const updateAssessment = async (id: number, assessmentData: Assessment): Promise<Assessment> => {
    const res = await new AssessmentService('evaluate').updateOne(id, assessmentData);
    const index = assessments.value.findIndex((q) => q.id === id);
    if (index !== -1) assessments.value[index] = res;
    if (currentAssessment.value?.id === id) currentAssessment.value = res;
    return res;
  };

  const removeAssessment = async (id: number): Promise<void> => {
    await new AssessmentService('evaluate').deleteOne(id);
    assessments.value = assessments.value.filter((q) => q.id !== id);
    if (currentAssessment.value?.id === id) {
      currentAssessment.value = null;
    }
  };

  // 🔹 ID Tracking Helpers
  const getAssessmentId = (): number | null => {
    return currentAssessment.value?.id || null;
  };

  const getItemBlockById = (id: number) => {
    return currentAssessment.value?.itemBlocks?.find((block) => block.id === id) || null;
  };

  const getHeaderBlockId = (): number | null => {
    const headerBlock = currentAssessment.value?.itemBlocks?.find(
      (block) => block.type === 'HEADER',
    );
    return headerBlock?.id || null;
  };

  const getRadioBlockId = (): number | null => {
    const radioBlock = currentAssessment.value?.itemBlocks?.find((block) => block.type === 'RADIO');
    return radioBlock?.id || null;
  };

  const getAllItemBlockIds = (): number[] => {
    return currentAssessment.value?.itemBlocks?.map((block) => block.id) || [];
  };

  // 🔹 Validation Helpers
  const validateIds = (): { valid: boolean; missing: string[] } => {
    const missing: string[] = [];

    if (!currentAssessment.value?.id) {
      missing.push('assessmentId');
    }

    if (!currentAssessment.value?.itemBlocks || currentAssessment.value.itemBlocks.length === 0) {
      missing.push('itemBlocks');
    } else {
      currentAssessment.value.itemBlocks.forEach((block, index) => {
        if (!block.id) {
          missing.push(`itemBlock[${index}].id`);
        }
        if (!block.assessmentId) {
          missing.push(`itemBlock[${index}].assessmentId`);
        }
      });
    }

    return {
      valid: missing.length === 0,
      missing,
    };
  };

  return {
    // state
    assessments,
    currentAssessment,
    loading,
    error,
    meta,
    page,
    limit,
    search,
    // actions
    fetchAssessmentById,
    // getAssessmentByIdWithSec,
    addAssessment,
    updateAssessment,
    removeAssessment,
    // ID tracking helpers
    getAssessmentId,
    getItemBlockById,
    getHeaderBlockId,
    getRadioBlockId,
    getAllItemBlockIds,
    validateIds,
  };
});
